import 'package:aqua_ui/aqua_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../../../core/mixins/analytics_mixin.dart';
import '../../../core/services/analytics_service.dart';
import '../../../core/utils/logger.dart';
import '../../../core/utils/payment_url_validator.dart';
import '../../../domain/entities/payments/payment_link.dart';
import '../../cubit/payment/payment_cubit.dart';
import '../../cubit/payment/payment_state.dart';

/// WebView widget for payment processing
class PaymentWebView extends StatefulWidget {
  final PaymentLink paymentLink;
  final VoidCallback? onCancel;
  final Function(String)? onUrlChanged;
  final Function(String)? onError;

  const PaymentWebView({
    super.key,
    required this.paymentLink,
    this.onCancel,
    this.onUrlChanged,
    this.onError,
  });

  @override
  State<PaymentWebView> createState() => _PaymentWebViewState();
}

class _PaymentWebViewState extends State<PaymentWebView> with AnalyticsMixin {
  late final WebViewController _controller;
  final AppLogger _logger = AppLogger();
  bool _isLoading = true;
  String? _currentUrl;
  String? _errorMessage;

  @override
  String get screenName => 'payment_webview';

  @override
  AnalyticsService get analytics => context.read<AnalyticsService>();

  @override
  void initState() {
    super.initState();
    _initializeWebView();
    _trackWebViewOpened();
  }

  void _initializeWebView() {
    _controller =
        WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setBackgroundColor(const Color(0x00000000))
          ..setNavigationDelegate(
            NavigationDelegate(
              onProgress: (int progress) {
                _updateLoadingState(progress < 100);
              },
              onPageStarted: (String url) {
                _logger.i(
                  'WebView page started: ${PaymentUrlValidator.sanitizeUrlForLogging(url)}',
                );
                _handleUrlChange(url);
              },
              onPageFinished: (String url) {
                _logger.i(
                  'WebView page finished: ${PaymentUrlValidator.sanitizeUrlForLogging(url)}',
                );
                _updateLoadingState(false);
                _handleUrlChange(url);
              },
              onWebResourceError: (WebResourceError error) {
                _handleWebViewError(error);
              },
              onNavigationRequest: (NavigationRequest request) {
                return _handleNavigationRequest(request);
              },
            ),
          );

    // Load the payment URL
    _loadPaymentUrl();
  }

  void _loadPaymentUrl() {
    try {
      final url = widget.paymentLink.paymentLinkUrl;

      // Validate URL before loading
      if (!PaymentUrlValidator.isValidPaymentUrl(url)) {
        _handleError('Invalid payment URL');
        return;
      }

      _logger.i('Loading payment URL');
      _controller.loadRequest(Uri.parse(url));

      // Update cubit
      context.read<PaymentCubit>().startPaymentProcessing(url);
    } catch (e) {
      _logger.e('Error loading payment URL: $e');
      _handleError('Failed to load payment page');
    }
  }

  NavigationDecision _handleNavigationRequest(NavigationRequest request) {
    final url = request.url;

    _logger.i(
      'Navigation request: ${PaymentUrlValidator.sanitizeUrlForLogging(url)}',
    );

    // Allow navigation to payment gateway and callback URLs
    if (PaymentUrlValidator.isValidPaymentUrl(url)) {
      return NavigationDecision.navigate;
    }

    // Handle external URLs by opening in external browser
    if (url.startsWith('http://') || url.startsWith('https://')) {
      _logger.w('Blocking external navigation: $url');
      _handleError('External navigation blocked for security');
      return NavigationDecision.prevent;
    }

    return NavigationDecision.navigate;
  }

  void _handleUrlChange(String url) {
    setState(() {
      _currentUrl = url;
    });

    // Notify parent widget
    widget.onUrlChanged?.call(url);

    // Update cubit with current URL
    context.read<PaymentCubit>().updateCurrentUrl(url);

    // Track URL changes
    if (PaymentUrlValidator.isPaymentCompletionUrl(url)) {
      trackEvent(
        'payment_completion_url_detected',
        params: {
          'is_success': PaymentUrlValidator.isSuccessUrl(url),
          'payment_link_id': widget.paymentLink.paymentLinkId,
        },
      );
    }
  }

  void _updateLoadingState(bool isLoading) {
    if (_isLoading != isLoading) {
      setState(() {
        _isLoading = isLoading;
      });

      // Update cubit
      context.read<PaymentCubit>().updateWebViewLoading(isLoading);
    }
  }

  void _handleWebViewError(WebResourceError error) {
    _logger.e('WebView error: ${error.description} (Code: ${error.errorCode})');

    String errorMessage;
    switch (error.errorCode) {
      case -2: // Network error
        errorMessage = 'Network error. Please check your internet connection.';
        break;
      case -6: // Connection refused
        errorMessage = 'Connection failed. Please try again.';
        break;
      case -8: // Timeout
        errorMessage = 'Request timeout. Please try again.';
        break;
      default:
        errorMessage = 'An error occurred while loading the payment page.';
    }

    _handleError(errorMessage);

    trackEvent(
      'payment_webview_error',
      params: {
        'error_code': error.errorCode,
        'error_description': error.description,
        'payment_link_id': widget.paymentLink.paymentLinkId,
      },
    );
  }

  void _handleError(String message) {
    setState(() {
      _errorMessage = message;
    });

    widget.onError?.call(message);
    _logger.e('Payment WebView error: $message');
  }

  void _handleCancel() {
    _logger.i('Payment cancelled by user');

    trackEvent(
      'payment_cancelled_by_user',
      params: {
        'payment_link_id': widget.paymentLink.paymentLinkId,
        'current_url': PaymentUrlValidator.sanitizeUrlForLogging(
          _currentUrl ?? '',
        ),
      },
    );

    context.read<PaymentCubit>().cancelPayment(reason: 'User cancelled');
    widget.onCancel?.call();
  }

  void _trackWebViewOpened() {
    trackEvent(
      'payment_webview_opened',
      params: {
        'payment_link_id': widget.paymentLink.paymentLinkId,
        'amount': widget.paymentLink.amount,
        'currency': widget.paymentLink.currency,
      },
    );
  }

  void _retryLoading() {
    setState(() {
      _errorMessage = null;
      _isLoading = true;
    });

    trackEvent(
      'payment_webview_retry',
      params: {'payment_link_id': widget.paymentLink.paymentLinkId},
    );

    _loadPaymentUrl();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: AquaText.headline('Payment'),
        backgroundColor: acPrimaryBlue,
        foregroundColor: acWhiteColor,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: _handleCancel,
          tooltip: 'Cancel Payment',
        ),
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(acWhiteColor),
                ),
              ),
            ),
        ],
      ),
      body: BlocListener<PaymentCubit, PaymentState>(
        listener: (context, state) {
          if (state is PaymentCompleted) {
            // Payment completed, close WebView
            Navigator.of(context).pop();
          } else if (state is PaymentError) {
            _handleError(state.message);
          } else if (state is PaymentCancelled) {
            Navigator.of(context).pop();
          }
        },
        child: _buildWebViewContent(),
      ),
    );
  }

  Widget _buildWebViewContent() {
    if (_errorMessage != null) {
      return _buildErrorView();
    }

    return Stack(
      children: [
        WebViewWidget(controller: _controller),
        if (_isLoading) _buildLoadingOverlay(),
      ],
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red.shade400),
            const SizedBox(height: 16),
            AquaText.headline('Payment Error', color: Colors.red.shade700),
            const SizedBox(height: 8),
            AquaText.body(
              _errorMessage!,
              textAlign: TextAlign.center,
              color: Colors.grey.shade700,
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: _retryLoading,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: acPrimaryBlue,
                    foregroundColor: acWhiteColor,
                  ),
                  child: AquaText.body('Retry'),
                ),
                OutlinedButton(
                  onPressed: _handleCancel,
                  child: AquaText.body('Cancel'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.white.withOpacity(0.8),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(acPrimaryBlue),
            ),
            SizedBox(height: 16),
            Text(
              'Loading payment page...',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _logger.i('Payment WebView disposed');
    super.dispose();
  }
}
