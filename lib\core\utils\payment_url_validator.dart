import '../constants/app_constants.dart';
import 'logger.dart';

/// Utility class for validating payment URLs
class PaymentUrlValidator {
  static final AppLogger _logger = AppLogger();

  /// List of allowed domains for payment processing
  static const List<String> _allowedDomains = [
    'payments.zoho.in',
    'aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net',
    'localhost', // For development
  ];

  /// List of allowed payment paths
  static const List<String> _allowedPaths = [
    '/checkout/',
    '/payment/',
    '/api/payment/',
    '/api/payment-page',
  ];

  /// Validate if a URL is safe for payment processing
  static bool isValidPaymentUrl(String url) {
    try {
      if (url.trim().isEmpty) {
        _logger.w('Payment URL is empty');
        return false;
      }

      final uri = Uri.parse(url);

      // Check if the URL uses HTTPS (or HTTP for localhost)
      if (!_isSecureScheme(uri)) {
        _logger.w('Payment URL is not secure: $url');
        return false;
      }

      // Check if the domain is allowed
      if (!_isAllowedDomain(uri)) {
        _logger.w('Payment URL domain not allowed: ${uri.host}');
        return false;
      }

      // Check if the path is allowed
      if (!_isAllowedPath(uri)) {
        _logger.w('Payment URL path not allowed: ${uri.path}');
        return false;
      }

      _logger.i('Payment URL validation passed: $url');
      return true;
    } catch (e) {
      _logger.e('Error validating payment URL: $e');
      return false;
    }
  }

  /// Check if URL indicates payment completion
  static bool isPaymentCompletionUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.path.contains('/payment/success') || 
             uri.path.contains('/payment/failed');
    } catch (e) {
      _logger.e('Error checking payment completion URL: $e');
      return false;
    }
  }

  /// Check if URL is a success page
  static bool isSuccessUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.path.contains('/payment/success');
    } catch (e) {
      _logger.e('Error checking success URL: $e');
      return false;
    }
  }

  /// Check if URL is a failure page
  static bool isFailureUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.path.contains('/payment/failed');
    } catch (e) {
      _logger.e('Error checking failure URL: $e');
      return false;
    }
  }

  /// Extract payment parameters from URL
  static Map<String, String> extractPaymentParameters(String url) {
    try {
      final uri = Uri.parse(url);
      return Map<String, String>.from(uri.queryParameters);
    } catch (e) {
      _logger.e('Error extracting payment parameters: $e');
      return {};
    }
  }

  /// Check if the URL scheme is secure
  static bool _isSecureScheme(Uri uri) {
    return uri.scheme == 'https' || 
           (uri.scheme == 'http' && uri.host == 'localhost');
  }

  /// Check if the domain is in the allowed list
  static bool _isAllowedDomain(Uri uri) {
    return _allowedDomains.any((domain) => 
        uri.host == domain || uri.host.endsWith('.$domain'));
  }

  /// Check if the path is allowed for payment processing
  static bool _isAllowedPath(Uri uri) {
    // Allow all paths for completion URLs
    if (isPaymentCompletionUrl(uri.toString())) {
      return true;
    }

    // Check if path starts with any allowed path
    return _allowedPaths.any((allowedPath) => 
        uri.path.startsWith(allowedPath));
  }

  /// Sanitize URL for logging (remove sensitive parameters)
  static String sanitizeUrlForLogging(String url) {
    try {
      final uri = Uri.parse(url);
      final sanitizedParams = <String, String>{};

      // Keep only non-sensitive parameters
      const allowedParams = [
        'status',
        'payment_link_id',
        'callback_source',
        'invoice',
      ];

      for (final param in allowedParams) {
        if (uri.queryParameters.containsKey(param)) {
          sanitizedParams[param] = uri.queryParameters[param]!;
        }
      }

      return uri.replace(queryParameters: sanitizedParams).toString();
    } catch (e) {
      return 'Invalid URL';
    }
  }

  /// Get the base URL for the current environment
  static String getBaseUrl() {
    return AppConstants.baseUrl;
  }

  /// Get the callback URL for payment processing
  static String getCallbackUrl() {
    return '${getBaseUrl()}/api/payment/callback';
  }

  /// Get the success page URL
  static String getSuccessPageUrl() {
    return '${getBaseUrl()}/payment/success';
  }

  /// Get the failure page URL
  static String getFailurePageUrl() {
    return '${getBaseUrl()}/payment/failed';
  }
}
